package com.midas.crm;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import jakarta.annotation.PostConstruct;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;

@SpringBootApplication(exclude = { RabbitAutoConfiguration.class })
@EnableScheduling
@EnableAsync
public class CrmApplication {

	@Bean
	@Primary
	public ObjectMapper objectMapperCustomizer(ObjectMapper objectMapper, Module hibernateJacksonModule) {
		// Registrar el módulo de Hibernate
		objectMapper.registerModule(hibernateJacksonModule);

		// Asegurarse de que las fechas se serialicen como strings ISO-8601
		objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

		return objectMapper;
	}

	public static void main(String[] args) {
		SpringApplication.run(CrmApplication.class, args);
	}

	@PostConstruct
	public void init() {
		TimeZone.setDefault(TimeZone.getTimeZone("America/Lima"));
	}

}

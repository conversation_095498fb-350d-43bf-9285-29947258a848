/* Estilos para el componente de gráficos de rendimiento de leads */

.graficos-rendimiento-container {
  .filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;

    .filter-group {
      display: flex;
      flex-direction: column;

      label {
        font-size: 0.75rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.25rem;
      }

      select,
      input {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        background-color: white;
        color: #111827;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px #3b82f6;
          border-color: transparent;
        }

        &:disabled {
          background-color: #f3f4f6;
          color: #6b7280;
          cursor: not-allowed;
        }
      }
    }
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;

    @media (min-width: 640px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .stat-card {
      border-radius: 0.5rem;
      padding: 1rem;
      color: white;

      &.leads-card {
        background: linear-gradient(to right, #3b82f6, #2563eb);
      }

      &.asesores-card {
        background: linear-gradient(to right, #10b981, #059669);
      }

      &.promedio-card {
        background: linear-gradient(to right, #8b5cf6, #7c3aed);
      }

      .stat-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .stat-info {
          .stat-label {
            font-size: 0.875rem;
            font-weight: 500;
            opacity: 0.9;
          }

          .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
          }
        }

        .stat-icon {
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 9999px;
          padding: 0.75rem;

          svg {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
    }
  }

  .chart-container {
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
      }

      .loading-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6b7280;

        svg {
          width: 1rem;
          height: 1rem;
        }

        span {
          font-size: 0.875rem;
        }
      }
    }

    .chart-wrapper {
      position: relative;
      width: 100%;
      height: 400px;

      canvas {
        width: 100%;
        height: 100%;
      }

      .no-data-message {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9fafb;
        border-radius: 0.5rem;

        .no-data-content {
          text-align: center;

          svg {
            margin: 0 auto 1rem auto;
            width: 3rem;
            height: 3rem;
            color: #9ca3af;
          }

          p {
            color: #6b7280;

            &.subtitle {
              font-size: 0.875rem;
              color: #9ca3af;
              margin-top: 0.25rem;
            }
          }
        }
      }
    }
  }

  .refresh-button {
    padding: 0.5rem 1rem;
    background-color: #2563eb;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: #1d4ed8;
    }

    &:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }

    svg {
      width: 1rem;
      height: 1rem;

      &.spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
}

/* Animaciones */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive */
@media (max-width: 640px) {
  .graficos-rendimiento-container {
    .filter-controls {
      flex-direction: column;
      align-items: stretch;

      .filter-group {
        width: 100%;
      }
    }

    .stats-cards {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .graficos-rendimiento-container {
    .chart-container {
      padding: 1rem;

      .chart-wrapper {
        height: 300px;
      }
    }
  }
}

/* Hover effects */
.stat-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  filter,
  take,
  debounceTime,
  timeout,
  map,
  catchError,
  of,
} from 'rxjs';
import { WebSocketService } from '../websocket/WebSocketService';
import { Notificacion, TipoNotificacion } from '@app/models/notificacion.model';
import {
  NotificationReadUser,
  NotificationReadersResponse,
} from '@app/models/notification-read-user.model';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import { GeneralService } from '../general.service';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root',
})
export class NotificacionesWsService {
  // BehaviorSubjects para los datos
  private notificaciones$ = new BehaviorSubject<Notificacion[]>([]);
  private contadorNoLeidas$ = new BehaviorSubject<number>(0);
  private cargando$ = new BehaviorSubject<boolean>(false);

  // Control de estado
  private initialized = false;
  private static servicioInicializado = false;
  private static instance: NotificacionesWsService;

  // Gestión de suscripciones
  private subscriptions: Subscription[] = [];

  // Control de solicitudes
  private ultimaSolicitud = 0;

  constructor(
    private webSocketService: WebSocketService,
    private http: HttpClient,
    private authService: GeneralService
  ) {
    // Implementar patrón Singleton
    if (NotificacionesWsService.instance) {
      return NotificacionesWsService.instance;
    }

    NotificacionesWsService.instance = this;

    // Suscribirse al estado de conexión del WebSocket
    this.addSubscription(
      'connection',
      this.webSocketService.getConnectionStatus().subscribe((connected) => {
        if (connected && !this.initialized) {
          // WebSocket conectado, inicializar suscripciones
          this.setupSubscriptions();
        } else if (connected && this.initialized) {
          // Si ya está inicializado y se reconecta, solicitar notificaciones
          this.solicitarNotificaciones();
        }
      })
    );
  }

  /**
   * Añade una suscripción al registro para limpieza posterior
   * Evita suscripciones duplicadas usando un nombre único
   */
  private addSubscription(name: string, subscription: Subscription): void {
    // Buscar si ya existe una suscripción con este nombre
    const existingIndex = this.subscriptions.findIndex(
      (sub) => (sub as any)._name === name
    );

    // Si existe, eliminarla primero
    if (existingIndex !== -1) {
      this.subscriptions[existingIndex].unsubscribe();
      this.subscriptions.splice(existingIndex, 1);
    }

    // Añadir un nombre a la suscripción para identificarla
    (subscription as any)._name = name;

    // Añadir suscripción al registro
    this.subscriptions.push(subscription);
  }

  /**
   * Inicializa el servicio de notificaciones
   */
  inicializar(): void {
    // Evitar inicialización múltiple
    if (this.initialized) {
      console.log(
        'Servicio de notificaciones ya inicializado, omitiendo inicialización'
      );
      return;
    }

    // Evitar inicializaciones duplicadas a nivel global
    if (NotificacionesWsService.servicioInicializado) {
      console.log('Servicio de notificaciones ya inicializado globalmente');
      return;
    }

    console.log('Inicializando servicio de notificaciones');

    // Marcar como inicializado para evitar múltiples inicializaciones
    NotificacionesWsService.servicioInicializado = true;

    // Verificar si el WebSocket está conectado
    if (!this.webSocketService.isConnected()) {
      console.log('WebSocket no conectado, intentando conectar...');
      this.webSocketService.connect();
    } else {
      console.log('WebSocket ya conectado');
      // Si ya está conectado, configurar suscripciones inmediatamente
      this.setupSubscriptions();
    }

    // Cargar notificaciones reales después de un pequeño retraso
    // para dar tiempo a que se establezca la conexión WebSocket
    setTimeout(() => {
      this.solicitarNotificaciones();
    }, 500);
  }

  /**
   * Verifica si el servicio está inicializado
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Configura las suscripciones a los canales WebSocket
   * Método público para permitir inicialización desde componentes
   */
  setupSubscriptions(): void {
    if (this.initialized) {
      console.log('Suscripciones ya configuradas, omitiendo');
      return;
    }

    // Verificar si el WebSocket está conectado
    if (!this.webSocketService.isConnected()) {
      // Si no está conectado, intentar conectar primero
      console.log('WebSocket no conectado, intentando conectar...');
      this.webSocketService.connect();

      // Suscribirse al estado de conexión para inicializar cuando se conecte
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter((connected) => connected),
          take(1)
        )
        .subscribe(() => {
          // Cuando se conecte, intentar inicializar nuevamente
          console.log('WebSocket conectado, configurando suscripciones...');
          this.setupSubscriptions();

          // Solicitar notificaciones una vez que las suscripciones estén configuradas
          // y la conexión esté establecida
          setTimeout(() => {
            this.solicitarNotificaciones();
          }, 300);
        });

      this.subscriptions.push(sub);

      // No marcar como inicializado aún, se intentará nuevamente cuando se conecte
      return;
    }

    try {
      // Limpiar suscripciones existentes para evitar duplicados
      this.subscriptions.forEach((sub) => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
      this.subscriptions = [];

      const usuarioId = this.authService.getUserId();
      if (!usuarioId) {
        console.log(
          'No hay ID de usuario, no se pueden configurar suscripciones'
        );
        return;
      }

      console.log(`Configurando suscripciones para el usuario ${usuarioId}`);

      // Asegurarse de que el WebSocket esté suscrito a los tópicos necesarios
      // Tópico principal de notificaciones del usuario
      this.webSocketService.subscribeToDynamicTopic(
        `/user/${usuarioId}/queue/notifications`,
        'NOTIFICATIONS'
      );

      // Tópico para el contador de notificaciones no leídas
      this.webSocketService.subscribeToDynamicTopic(
        `/user/${usuarioId}/queue/notifications.count`,
        'NOTIFICATIONS_COUNT'
      );

      // Tópico para notificaciones marcadas como leídas
      this.webSocketService.subscribeToDynamicTopic(
        `/user/${usuarioId}/queue/notifications.read`,
        'NOTIFICATIONS_READ'
      );

      // Tópico para todas las notificaciones del usuario
      this.webSocketService.subscribeToDynamicTopic(
        `/user/${usuarioId}/queue/notifications.all`,
        'NOTIFICATIONS_ALL'
      );

      // Tópico para notificaciones broadcast (para todos los usuarios)
      this.webSocketService.subscribeToDynamicTopic(
        '/topic/notifications',
        'NOTIFICATIONS_BROADCAST'
      );

      // Suscribirse a nuevas notificaciones
      this.addSubscription(
        'notifications',
        this.webSocketService
          .getMessagesByDestination(`/user/${usuarioId}/queue/notifications`)
          .subscribe((notificacion) => {
            console.log('Notificación recibida para el usuario:', notificacion);
            this.procesarNuevaNotificacion(notificacion);
          })
      );

      // Suscribirse a actualizaciones de contador de notificaciones
      this.addSubscription(
        'count',
        this.webSocketService
          .getMessagesByDestination(
            `/user/${usuarioId}/queue/notifications.count`
          )
          .subscribe((contador) => {
            console.log('Contador de notificaciones recibido:', contador);
            if (typeof contador === 'number') {
              // Verificar si el contador actual es diferente al recibido
              const contadorActual = this.contadorNoLeidas$.getValue();
              console.log(
                `Contador actual: ${contadorActual}, contador recibido: ${contador}`
              );

              // Solo actualizar si el contador recibido es diferente al actual
              // Esto evita que se sobrescriba el contador local después de marcar como leída
              if (contadorActual !== contador) {
                console.log(
                  'Actualizando contador de notificaciones a:',
                  contador
                );
                this.contadorNoLeidas$.next(contador);
              } else {
                console.log(
                  'Contador recibido es igual al actual, no se actualiza'
                );
              }
            }
          })
      );

      // Suscribirse a actualizaciones de notificaciones leídas
      this.addSubscription(
        'read',
        this.webSocketService
          .getMessagesByDestination(
            `/user/${usuarioId}/queue/notifications.read`
          )
          .subscribe((notificacionesLeidas) => {
            console.log(
              'Notificaciones marcadas como leídas:',
              notificacionesLeidas
            );
            this.actualizarNotificacionesLeidas(notificacionesLeidas);
          })
      );

      // Suscribirse a todas las notificaciones del usuario
      this.addSubscription(
        'all',
        this.webSocketService
          .getMessagesByDestination(
            `/user/${usuarioId}/queue/notifications.all`
          )
          .pipe(debounceTime(300))
          .subscribe((notificaciones) => {
            console.log('Lista de notificaciones recibida:', notificaciones);
            if (Array.isArray(notificaciones)) {
              const notificacionesFormateadas = notificaciones.map((n) =>
                this.convertirFormatoNotificacion(n)
              );
              this.notificaciones$.next(notificacionesFormateadas);
              this.actualizarContadorNoLeidas();
              this.cargando$.next(false);
            }
          })
      );

      // Suscribirse a notificaciones broadcast (para todos los usuarios)
      this.addSubscription(
        'broadcast',
        this.webSocketService
          .getMessagesByDestination('/topic/notifications')
          .subscribe((notificacion) => {
            console.log('Notificación broadcast recibida:', notificacion);
            this.procesarNuevaNotificacion(notificacion);
          })
      );

      // Suscribirse a los eventos de conexión establecida
      this.addSubscription(
        'connection-established',
        this.webSocketService
          .getMessagesByType('CONNECTION_ESTABLISHED')
          .subscribe(() => {
            console.log(
              'Conexión WebSocket establecida, solicitando notificaciones'
            );
            // Solicitar notificaciones cuando se establece la conexión
            this.solicitarNotificaciones();
          })
      );

      // Marcar como inicializado
      this.initialized = true;
      console.log('Suscripciones configuradas correctamente');
    } catch (error) {
      console.error('Error al configurar suscripciones:', error);
    }
  }

  /**
   * Procesa una nueva notificación recibida
   */
  private procesarNuevaNotificacion(notificacion: any): void {
    console.log('Procesando nueva notificación:', notificacion);

    if (!notificacion) {
      console.log('Notificación vacía, ignorando');
      return;
    }

    // Si es un array, procesar el array de notificaciones
    if (Array.isArray(notificacion)) {
      console.log('Recibido array de notificaciones:', notificacion.length);

      // Convertir el formato recibido al formato esperado por el componente
      const notificacionesFormateadas = notificacion.map((n: any) =>
        this.convertirFormatoNotificacion(n)
      );

      console.log('Notificaciones formateadas:', notificacionesFormateadas);
      this.notificaciones$.next(notificacionesFormateadas);
      this.actualizarContadorNoLeidas();
      this.cargando$.next(false);
      return;
    }

    // Si es un objeto con data que es un array, procesar el array
    if (notificacion.data && Array.isArray(notificacion.data)) {
      console.log(
        'Recibido objeto con array de notificaciones:',
        notificacion.data.length
      );

      // Convertir el formato recibido al formato esperado por el componente
      const notificacionesFormateadas = notificacion.data.map((n: any) =>
        this.convertirFormatoNotificacion(n)
      );

      console.log(
        'Notificaciones formateadas desde data:',
        notificacionesFormateadas
      );
      this.notificaciones$.next(notificacionesFormateadas);
      this.actualizarContadorNoLeidas();
      this.cargando$.next(false);
      return;
    }

    const notificacionesActuales = this.notificaciones$.getValue();

    // Verificar si la notificación tiene la estructura esperada
    if (!notificacion.id) {
      console.log('Notificación sin ID, ignorando:', notificacion);
      return;
    }

    // Convertir la notificación individual al formato esperado
    const notificacionFormateada =
      this.convertirFormatoNotificacion(notificacion);
    console.log('Notificación individual formateada:', notificacionFormateada);

    // Verificar si la notificación ya existe
    const indiceExistente = notificacionesActuales.findIndex(
      (n) => n.id === notificacionFormateada.id
    );

    if (indiceExistente >= 0) {
      // Actualizar notificación existente
      notificacionesActuales[indiceExistente] = notificacionFormateada;
    } else {
      // Agregar nueva notificación al principio de la lista
      notificacionesActuales.unshift(notificacionFormateada);
    }

    // Actualizar el observable
    this.notificaciones$.next([...notificacionesActuales]);

    // Actualizar contador de no leídas
    this.actualizarContadorNoLeidas();

    // Mostrar SweetAlert para nueva notificación (solo si es nueva, no existente)
    if (indiceExistente < 0 && !notificacionFormateada.leida) {
      this.mostrarSweetAlertNuevaNotificacion(notificacionFormateada);
    }
  }

  /**
   * Convierte una notificación del formato recibido del backend al formato esperado por el componente
   */
  private convertirFormatoNotificacion(notificacion: any): Notificacion {
    // Si ya tiene el formato esperado, devolverlo tal cual
    if (
      notificacion.titulo !== undefined &&
      notificacion.mensaje !== undefined
    ) {
      return notificacion as Notificacion;
    }

    // Obtener el título de la notificación
    let titulo = 'Sin título';

    // Primero intentar usar el campo title directamente si existe
    if (notificacion.title) {
      titulo = notificacion.title;
    }
    // Como fallback, buscar en el campo data (para compatibilidad con notificaciones antiguas)
    else if (notificacion.data) {
      // Si data es un string, intentar parsearlo como JSON
      if (typeof notificacion.data === 'string') {
        try {
          const dataObj = JSON.parse(notificacion.data);
          if (dataObj && dataObj.title) {
            titulo = dataObj.title;
          }
        } catch (e) {
          console.warn('Error al parsear data como JSON:', e);
        }
      }
      // Si data es un objeto, usar directamente
      else if (
        typeof notificacion.data === 'object' &&
        notificacion.data !== null
      ) {
        if (notificacion.data.title) {
          titulo = notificacion.data.title;
        }
      }
    }

    // Mapear los campos del formato recibido al formato esperado
    return {
      id: notificacion.id,
      titulo: titulo, // Usar el título extraído del campo data
      mensaje: notificacion.message || 'Sin mensaje',
      fechaCreacion:
        notificacion.createdAt || notificacion.time || new Date().toISOString(),
      leida: notificacion.read === true,
      tipo: this.mapearTipoNotificacion(
        notificacion.type || notificacion.category
      ),
      enlace:
        notificacion.data && notificacion.data.link
          ? notificacion.data.link
          : notificacion.link || '',
      datos: notificacion,
      usuarioId: notificacion.recipientId || notificacion.userId || 0,
      senderName: notificacion.senderName || 'Sistema', // Agregar el nombre del remitente
    };
  }

  /**
   * Mapea el tipo de notificación del backend al enum TipoNotificacion
   */
  private mapearTipoNotificacion(tipo: string): TipoNotificacion {
    switch (tipo) {
      case 'USER_MESSAGE':
        return TipoNotificacion.INFO;
      case 'SYSTEM':
        return TipoNotificacion.SISTEMA;
      case 'ERROR':
        return TipoNotificacion.ERROR;
      case 'WARNING':
        return TipoNotificacion.ADVERTENCIA;
      case 'SUCCESS':
        return TipoNotificacion.EXITO;
      default:
        return TipoNotificacion.INFO;
    }
  }

  /**
   * Actualiza el estado de notificaciones marcadas como leídas
   */
  private actualizarNotificacionesLeidas(idsLeidas: number[] | any): void {
    console.log('Actualizando notificaciones leídas:', idsLeidas);

    // Si no hay datos, salir
    if (!idsLeidas) {
      console.log('No hay datos de notificaciones leídas');
      return;
    }

    // Si es un objeto con data que es un array, usar ese array
    if (idsLeidas.data && Array.isArray(idsLeidas.data)) {
      console.log('Usando array de IDs desde data:', idsLeidas.data);
      idsLeidas = idsLeidas.data;
    }

    // Si es un objeto con notificationId, convertirlo a un array con ese ID
    if (idsLeidas.notificationId !== undefined) {
      console.log(
        'Convirtiendo objeto con notificationId a array:',
        idsLeidas.notificationId
      );
      idsLeidas = [Number(idsLeidas.notificationId)];
    }

    // Si no es un array después de las conversiones, salir
    if (!Array.isArray(idsLeidas) || idsLeidas.length === 0) {
      console.log(
        'Los datos de notificaciones leídas no son un array válido:',
        idsLeidas
      );
      return;
    }

    const notificacionesActuales = this.notificaciones$.getValue();
    let actualizado = false;

    // Convertir todos los IDs a números para asegurar comparaciones correctas
    const idsLeidasNumericos = idsLeidas.map((id: any) => Number(id));

    // Marcar como leídas las notificaciones correspondientes
    notificacionesActuales.forEach((notificacion) => {
      const notificacionId = Number(notificacion.id);
      console.log(
        `Comparando notificación ID: ${notificacionId} con IDs leídas:`,
        idsLeidasNumericos
      );

      if (idsLeidasNumericos.includes(notificacionId) && !notificacion.leida) {
        console.log('Marcando como leída la notificación:', notificacionId);
        notificacion.leida = true;
        actualizado = true;
      }
    });

    if (actualizado) {
      console.log('Actualizando lista de notificaciones con las leídas');
      // Actualizar el observable
      this.notificaciones$.next([...notificacionesActuales]);

      // Actualizar contador de no leídas
      this.actualizarContadorNoLeidas();
    } else {
      console.log('No se actualizó ninguna notificación');
    }
  }

  /**
   * Actualiza el contador de notificaciones no leídas
   */
  private actualizarContadorNoLeidas(): void {
    const notificaciones = this.notificaciones$.getValue();
    const noLeidas = notificaciones.filter((n) => !n.leida).length;

    // Obtener el contador actual para comparar
    const contadorActual = this.contadorNoLeidas$.getValue();

    // Solo actualizar si el contador ha cambiado
    if (contadorActual !== noLeidas) {
      console.log(
        `Actualizando contador de no leídas: ${contadorActual} -> ${noLeidas}`
      );
      this.contadorNoLeidas$.next(noLeidas);

      // Notificar a los componentes que usan el contador
      this.notificarCambioContador(noLeidas);
    }
  }

  /**
   * Notifica a los componentes sobre cambios en el contador
   * Esto es útil para asegurar que todos los componentes se actualicen
   */
  private notificarCambioContador(contador: number): void {
    // Emitir un evento personalizado para que los componentes puedan reaccionar
    // Esto es útil cuando hay múltiples componentes que muestran el contador
    const event = new CustomEvent('notificaciones-contador-actualizado', {
      detail: { contador },
    });
    window.dispatchEvent(event);
  }

  /**
   * Solicita notificaciones al servidor
   * Este método es el punto de entrada principal para obtener notificaciones
   */
  solicitarNotificaciones(): void {
    const usuarioId = this.authService.getUserId();
    if (!usuarioId) {
      console.log(
        'No hay ID de usuario, no se pueden solicitar notificaciones'
      );
      // Si no hay usuario, no podemos hacer nada
      this.cargando$.next(false);
      return;
    }

    // Verificar si ya se está cargando
    if (this.cargando$.getValue()) {
      console.log('Ya se están cargando notificaciones, omitiendo solicitud');
      return;
    }

    // Verificar si ha pasado suficiente tiempo desde la última solicitud
    const ahora = Date.now();
    const THROTTLE_MS = 5000; // 5 segundos entre solicitudes
    if (ahora - this.ultimaSolicitud < THROTTLE_MS) {
      console.log(
        'Solicitud de notificaciones limitada por throttle, omitiendo'
      );
      return;
    }

    // Actualizar tiempo de última solicitud
    this.ultimaSolicitud = ahora;

    // Indicar que se están cargando notificaciones
    this.cargando$.next(true);

    // Verificar si WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      console.log('Solicitando notificaciones por WebSocket');

      // Enviar solicitud al endpoint correcto
      this.webSocketService.sendMessage('/app/notifications/all', {
        userId: usuarioId,
        limit: 10,
      });

      // No establecemos timeout para fallback a HTTP
      // Confiamos en que el WebSocket responderá eventualmente
    } else {
      // Si WebSocket no está conectado, intentar conectar y esperar
      console.log('WebSocket no conectado, intentando conectar...');
      this.webSocketService.connect();

      // Suscribirse al estado de conexión para solicitar notificaciones cuando se conecte
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter((connected) => connected),
          take(1)
        )
        .subscribe(() => {
          console.log(
            'WebSocket conectado, solicitando notificaciones por WebSocket'
          );

          // Enviar solicitud al endpoint correcto
          this.webSocketService.sendMessage('/app/notifications/all', {
            userId: usuarioId,
            limit: 10,
          });

          // No establecemos timeout para fallback a HTTP
          // Confiamos en que el WebSocket responderá eventualmente
        });

      this.subscriptions.push(sub);

      // No establecemos timeout para fallback a HTTP
      // Confiamos en que el WebSocket se conectará eventualmente
    }
  }

  /**
   * Método legacy para mantener compatibilidad
   * @deprecated Use solicitarNotificaciones() instead
   */
  cargarNotificaciones(): void {
    this.solicitarNotificaciones();
  }

  /**
   * Carga notificaciones usando HTTP
   * Este método es público para permitir su uso como fallback manual si es necesario
   */
  cargarNotificacionesPorHttp(): void {
    const usuarioId = this.authService.getUserId();
    if (!usuarioId) {
      this.cargando$.next(false);
      return;
    }

    console.log(
      `Cargando notificaciones por HTTP para el usuario ${usuarioId}`
    );

    // Usar el endpoint de polling que está implementado en el backend
    this.http
      .get<any>(`${environment.url}api/notifications/polling/${usuarioId}`)
      .subscribe({
        next: (respuesta) => {
          console.log('Respuesta de notificaciones por HTTP:', respuesta);

          if (respuesta && respuesta.rpta === 1 && respuesta.data) {
            // Verificar si hay notificaciones en la respuesta
            if (
              respuesta.data.latestNotifications &&
              Array.isArray(respuesta.data.latestNotifications)
            ) {
              this.notificaciones$.next(respuesta.data.latestNotifications);
              console.log(
                'Notificaciones cargadas por HTTP:',
                respuesta.data.latestNotifications.length
              );
            }

            // Actualizar el contador de no leídas
            if (respuesta.data.unreadCount !== undefined) {
              this.contadorNoLeidas$.next(respuesta.data.unreadCount);
              console.log(
                'Contador de no leídas actualizado por HTTP:',
                respuesta.data.unreadCount
              );
            }
          } else {
            console.log('Respuesta HTTP no tiene el formato esperado');
            // Inicializar con array vacío
            this.notificaciones$.next([]);
            this.contadorNoLeidas$.next(0);
          }

          this.cargando$.next(false);
        },
        error: (error) => {
          console.error('Error al cargar notificaciones por HTTP:', error);
          // Inicializar con array vacío
          this.notificaciones$.next([]);
          this.contadorNoLeidas$.next(0);
          this.cargando$.next(false);
        },
      });
  }

  /**
   * Marca una notificación como leída
   */
  marcarComoLeida(id: number): void {
    // Actualizar localmente primero para UI responsiva
    const notificaciones = this.notificaciones$.getValue();
    const notificacion = notificaciones.find((n) => n.id === id);

    if (notificacion && !notificacion.leida) {
      // Guardar el estado anterior para poder revertir si hay error
      const estadoAnterior = [...notificaciones];
      const contadorAnterior = this.contadorNoLeidas$.getValue();

      // Marcar como leída localmente
      notificacion.leida = true;
      this.notificaciones$.next([...notificaciones]);

      // Actualizar contador de no leídas
      const nuevoContador = contadorAnterior > 0 ? contadorAnterior - 1 : 0;
      console.log(
        `Actualizando contador de no leídas: ${contadorAnterior} -> ${nuevoContador}`
      );
      this.contadorNoLeidas$.next(nuevoContador);

      // Enviar al servidor
      const userId = this.authService.getUserId();
      if (!userId) {
        console.error(
          'No se pudo obtener el ID del usuario para marcar como leída'
        );
        return;
      }

      // Función para manejar errores y revertir cambios si es necesario
      const handleError = (error: any) => {
        console.error('Error al marcar como leída:', error);
        // Revertir cambios locales
        this.notificaciones$.next(estadoAnterior);
        this.contadorNoLeidas$.next(contadorAnterior);
      };

      if (this.webSocketService.isConnected()) {
        console.log('Marcando notificación como leída por WebSocket:', id);
        try {
          this.webSocketService.sendMessage('/app/notifications.markAsRead', {
            notificationId: id,
            userId: userId,
          });
        } catch (error) {
          handleError(error);
        }
      } else {
        // Si WebSocket no está conectado, intentar conectar y esperar
        console.log('WebSocket no conectado, intentando conectar...');
        this.webSocketService.connect();

        // Suscribirse al estado de conexión para enviar cuando se conecte
        const sub = this.webSocketService
          .getConnectionStatus()
          .pipe(
            filter((connected) => connected),
            take(1),
            timeout(5000) // Timeout de 5 segundos
          )
          .subscribe({
            next: () => {
              console.log(
                'WebSocket conectado, marcando notificación como leída:',
                id
              );
              try {
                this.webSocketService.sendMessage(
                  '/app/notifications.markAsRead',
                  {
                    notificationId: id,
                    userId: userId,
                  }
                );
              } catch (error) {
                handleError(error);
              }
            },
            error: (error) => {
              console.error('Error al conectar WebSocket:', error);
              handleError(error);
            },
          });

        this.subscriptions.push(sub);
      }
    }
  }

  /**
   * Marca todas las notificaciones como leídas
   */
  marcarTodasComoLeidas(): void {
    const notificaciones = this.notificaciones$.getValue();
    const idsNoLeidas = notificaciones.filter((n) => !n.leida).map((n) => n.id);

    if (idsNoLeidas.length === 0) {
      return;
    }

    // Actualizar localmente primero
    notificaciones.forEach((n) => (n.leida = true));
    this.notificaciones$.next([...notificaciones]);
    this.contadorNoLeidas$.next(0);

    // Enviar al servidor
    const userId = this.authService.getUserId();
    if (!userId) {
      console.error(
        'No se pudo obtener el ID del usuario para marcar todas como leídas'
      );
      return;
    }

    if (this.webSocketService.isConnected()) {
      console.log(
        'Marcando todas las notificaciones como leídas por WebSocket'
      );
      this.webSocketService.sendMessage('/app/notifications.readAll', {
        userId: userId,
      });
    } else {
      // Si WebSocket no está conectado, intentar conectar y esperar
      console.log('WebSocket no conectado, intentando conectar...');
      this.webSocketService.connect();

      // Suscribirse al estado de conexión para enviar cuando se conecte
      const sub = this.webSocketService
        .getConnectionStatus()
        .pipe(
          filter((connected) => connected),
          take(1)
        )
        .subscribe(() => {
          console.log(
            'WebSocket conectado, marcando todas las notificaciones como leídas'
          );
          this.webSocketService.sendMessage('/app/notifications.readAll', {
            userId: userId,
          });
        });

      this.subscriptions.push(sub);
    }
  }

  /**
   * Obtiene el observable de notificaciones
   */
  getNotificaciones(): Observable<Notificacion[]> {
    return this.notificaciones$.asObservable();
  }

  /**
   * Obtiene el observable del contador de notificaciones no leídas
   */
  getContadorNoLeidas(): Observable<number> {
    return this.contadorNoLeidas$.asObservable();
  }

  /**
   * Obtiene el observable del estado de carga
   */
  getCargando(): Observable<boolean> {
    return this.cargando$.asObservable();
  }

  /**
   * Limpia las suscripciones cuando se destruye el servicio
   */
  destruir(): void {
    // Cancelar todas las suscripciones
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];

    // Reiniciar el estado
    this.initialized = false;
  }

  /**
   * Obtiene la lista de usuarios que han leído una notificación específica
   */
  obtenerUsuariosQueHanLeido(
    notificationId: number
  ): Observable<NotificationReadUser[]> {
    return this.http
      .get<any>(`${environment.url}api/notifications/${notificationId}/readers`)
      .pipe(
        map((response: any) => {
          if (response && response.rpta === 1 && response.data) {
            return response.data as NotificationReadUser[];
          }
          return [];
        }),
        catchError((error) => {
          console.error(
            'Error al obtener usuarios que han leído la notificación:',
            error
          );
          return of([]);
        })
      );
  }

  /**
   * Obtiene el número de usuarios que han leído una notificación específica
   */
  obtenerContadorLecturas(notificationId: number): Observable<number> {
    return this.http
      .get<any>(
        `${environment.url}api/notifications/${notificationId}/read-count`
      )
      .pipe(
        map((response: any) => {
          if (response && response.rpta === 1 && response.data !== undefined) {
            return response.data as number;
          }
          return 0;
        }),
        catchError((error) => {
          console.error('Error al obtener contador de lecturas:', error);
          return of(0);
        })
      );
  }

  /**
   * Suscribirse a actualizaciones en tiempo real de usuarios que han leído notificaciones
   */
  suscribirseAActualizacionesLecturas(
    notificationId: number
  ): Observable<NotificationReadersResponse> {
    const topic = `/topic/notifications/${notificationId}/readers`;

    // Suscribirse al tópico específico de la notificación
    this.webSocketService.subscribeToDynamicTopic(
      topic,
      `READERS_${notificationId}`
    );

    return this.webSocketService.getMessagesByDestination(topic).pipe(
      map((data: any) => {
        if (data && data.readers && Array.isArray(data.readers)) {
          return {
            readers: data.readers as NotificationReadUser[],
            readCount: data.readCount || 0,
            notificationId: data.notificationId || notificationId,
          } as NotificationReadersResponse;
        }
        return {
          readers: [],
          readCount: 0,
          notificationId: notificationId,
        } as NotificationReadersResponse;
      }),
      catchError((error) => {
        console.error(
          'Error en suscripción a actualizaciones de lecturas:',
          error
        );
        return of({
          readers: [],
          readCount: 0,
          notificationId: notificationId,
        } as NotificationReadersResponse);
      })
    );
  }

  /**
   * Muestra un SweetAlert cuando llega una nueva notificación
   */
  private mostrarSweetAlertNuevaNotificacion(notificacion: Notificacion): void {
    // Obtener el nombre del remitente desde los datos de la notificación
    let senderName = 'Sistema';

    // Intentar obtener el senderName de diferentes fuentes
    if (notificacion.senderName) {
      senderName = notificacion.senderName;
    } else if (notificacion.datos) {
      try {
        const datos =
          typeof notificacion.datos === 'string'
            ? JSON.parse(notificacion.datos)
            : notificacion.datos;
        if (datos.senderName) {
          senderName = datos.senderName;
        }
      } catch (error) {
        console.warn('Error al parsear datos de notificación:', error);
      }
    }

    let titulo = '';
    let mensaje = '';
    let icono: 'success' | 'info' = 'info';
    let iconoEmoji = '';

    // Determinar si es una notificación personal o broadcast
    // Las notificaciones broadcast tienen type 'BROADCAST' o no tienen usuarioId específico
    const esBroadcast =
      notificacion.datos &&
      (notificacion.datos.type === 'BROADCAST' ||
        !notificacion.usuarioId ||
        notificacion.usuarioId === 0);

    if (esBroadcast) {
      titulo = 'Nueva Notificación';
      mensaje = `Notificación nueva de ${senderName}: "${notificacion.titulo}"`;
      icono = 'success';
      iconoEmoji = '📢';
    } else {
      titulo = 'Nueva Notificación Personal';
      mensaje = `Te ha llegado una notificación personal de ${senderName}: "${notificacion.titulo}"`;
      icono = 'info';
      iconoEmoji = '👤';
    }

    // Mostrar SweetAlert en la esquina superior derecha con Tailwind CSS
    Swal.fire({
      title: titulo,
      text: mensaje,
      icon: icono,
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 8000, // 8 segundos para notificaciones recibidas
      timerProgressBar: true,
      background: '#ffffff',
      color: '#374151',
      customClass: {
        popup: 'swal-notification-toast',
        title: 'swal-notification-title',
      },
      didOpen: (toast) => {
        // Aplicar estilos con Tailwind CSS usando clases
        toast.className +=
          ' mt-16 mr-4 max-w-sm rounded-xl shadow-2xl border border-gray-200 font-sans';

        // Agregar el emoji al título
        const titleElement = toast.querySelector('.swal2-title');
        if (titleElement) {
          titleElement.innerHTML = `${iconoEmoji} ${titulo}`;
          titleElement.className +=
            ' text-base font-semibold text-gray-800 mb-2';
        }

        // Estilizar el contenido
        const contentElement = toast.querySelector('.swal2-html-container');
        if (contentElement) {
          contentElement.className += ' text-sm text-gray-600 leading-relaxed';
        }

        // Estilizar la barra de progreso
        const progressBar = toast.querySelector('.swal2-timer-progress-bar');
        if (progressBar) {
          if (icono === 'success') {
            progressBar.className += ' bg-green-500';
          } else {
            progressBar.className += ' bg-blue-500';
          }
        }

        // Agregar borde lateral colorido
        if (icono === 'success') {
          toast.style.borderLeft = '4px solid #10b981';
        } else {
          toast.style.borderLeft = '4px solid #3b82f6';
        }

        // Configurar z-index alto para aparecer sobre el header
        toast.style.zIndex = '10000';

        // Animación de entrada
        toast.style.animation = 'slideInFromRight 0.4s ease-out';

        // Efecto hover para pausar el timer
        toast.addEventListener('mouseenter', () => {
          Swal.stopTimer();
          toast.style.transform = 'scale(1.02)';
          toast.style.transition = 'transform 0.2s ease';
        });

        toast.addEventListener('mouseleave', () => {
          Swal.resumeTimer();
          toast.style.transform = 'scale(1)';
        });

        // Hacer clic en el toast para ir a la notificación (si tiene enlace)
        if (notificacion.enlace) {
          toast.style.cursor = 'pointer';
          toast.addEventListener('click', () => {
            // Aquí podrías agregar navegación si es necesario
            console.log('Navegando a:', notificacion.enlace);
            Swal.close();
          });
        }
      },
    });
  }
}

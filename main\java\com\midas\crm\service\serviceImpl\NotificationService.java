package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.NotificationDTO;
import com.midas.crm.entity.DTO.NotificationReadUserDTO;
import com.midas.crm.entity.Notification;
import com.midas.crm.entity.NotificationRead;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.NotificationReadRepository;
import com.midas.crm.repository.NotificationRepository;
import com.midas.crm.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Servicio para gestionar las notificaciones
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final NotificationRepository notificationRepository;
    private final NotificationReadRepository notificationReadRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Crea una nueva notificación
     *
     * @param notificationDTO DTO con los datos de la notificación
     * @return DTO de la notificación creada
     */
    @Transactional
    public NotificationDTO createNotification(NotificationDTO notificationDTO) {
        Notification notification = new Notification();

        // Configurar destinatario si se proporciona
        if (notificationDTO.getRecipientId() != null) {
            userRepository.findById(notificationDTO.getRecipientId())
                    .ifPresent(notification::setRecipient);
        }

        // Configurar remitente si se proporciona
        if (notificationDTO.getSenderId() != null) {
            userRepository.findById(notificationDTO.getSenderId())
                    .ifPresent(notification::setSender);
        }

        // Configurar datos básicos
        notification.setSenderName(notificationDTO.getSenderName());
        notification.setTitle(notificationDTO.getTitle());
        notification.setMessage(notificationDTO.getMessage());
        notification.setAvatar(notificationDTO.getAvatar());
        notification.setType(notificationDTO.getType());
        notification.setCategory(notificationDTO.getCategory());
        notification.setCreatedAt(LocalDateTime.now());
        notification.setRead(false);

        // Guardar la notificación
        Notification savedNotification = notificationRepository.save(notification);

        // Convertir a DTO
        NotificationDTO savedDTO = mapToDTO(savedNotification);

        // Enviar notificación en tiempo real al destinatario específico
        if (notification.getRecipientId() != null) {
            messagingTemplate.convertAndSendToUser(
                    notification.getRecipientId().toString(),
                    "/queue/notifications",
                    savedDTO);
        }

        // Si es una notificación de tipo BROADCAST, enviarla a todos los usuarios
        if (notification.getType() == Notification.NotificationType.BROADCAST) {
            messagingTemplate.convertAndSend("/topic/notifications", savedDTO);
        }

        // Si es una notificación basada en rol, enviarla al tópico correspondiente
        if (notification.getType() == Notification.NotificationType.ROLE_BASED && notification.getData() != null) {
            messagingTemplate.convertAndSend("/topic/notifications/role/" + notification.getData(), savedDTO);
        }

        return savedDTO;
    }

    /**
     * Obtiene las notificaciones para un usuario
     *
     * @param userId ID del usuario
     * @param page   Número de página
     * @param size   Tamaño de página
     * @return Página de notificaciones
     */
    public Page<NotificationDTO> getNotificationsForUser(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Notification> notifications = notificationRepository.findByRecipientIdOrBroadcast(userId, pageable);
        return notifications.map(this::mapToDTO);
    }

    /**
     * Marca una notificación como leída
     *
     * @param notificationId ID de la notificación
     * @param userId         ID del usuario
     * @return true si se marcó correctamente, false en caso contrario
     */
    @Transactional
    public boolean markAsRead(Long notificationId, Long userId) {
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);

        if (notificationOpt.isEmpty()) {
            return false;
        }

        Notification notification = notificationOpt.get();

        // Si es una notificación personal, actualizar el estado directamente
        if (notification.getRecipientId() != null && notification.getRecipientId().equals(userId)) {
            notification.setRead(true);
            notificationRepository.save(notification);
            return true;
        }

        // Si es una notificación broadcast o basada en rol, crear un registro de
        // lectura
        if (notification.getType() == Notification.NotificationType.BROADCAST ||
                notification.getType() == Notification.NotificationType.ROLE_BASED) {

            // Verificar si ya existe un registro de lectura
            if (notificationReadRepository.existsByUserIdAndNotificationId(userId, notificationId)) {
                return true;
            }

            // Crear nuevo registro de lectura
            NotificationRead notificationRead = new NotificationRead();
            notificationRead.setUserId(userId);
            notificationRead.setNotificationId(notificationId);
            notificationReadRepository.save(notificationRead);
            return true;
        }

        return false;
    }

    /**
     * Obtiene el número de notificaciones no leídas para un usuario
     *
     * @param userId ID del usuario
     * @return Número de notificaciones no leídas
     */
    public long getUnreadCount(Long userId) {
        return notificationRepository.countUnreadNotifications(userId);
    }

    /**
     * Obtiene las últimas notificaciones para un usuario
     *
     * @param userId ID del usuario
     * @param limit  Número máximo de notificaciones
     * @return Lista de notificaciones
     */
    public List<NotificationDTO> getLatestNotifications(Long userId, int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);

            // Como alternativa, usar el método legacy que no tiene problemas de conversión
            List<Notification> notifications = notificationRepository.findLatestNotifications(userId, pageable);

            // Convertir a DTOs y manejar el estado de lectura manualmente
            return notifications.stream()
                    .map(notification -> {
                        NotificationDTO dto = mapToDTO(notification);

                        // Para notificaciones broadcast, verificar si el usuario la ha leído
                        if (notification.getType() == Notification.NotificationType.BROADCAST) {
                            boolean isRead = notificationReadRepository.existsByUserIdAndNotificationId(userId,
                                    notification.getId());
                            dto.setRead(isRead);
                        }

                        return dto;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener notificaciones: {}", e.getMessage(), e);
            // Devolver una lista vacía en caso de error
            return new ArrayList<>();
        }
    }

    /**
     * Convierte una entidad Notification a DTO
     *
     * @param notification Entidad Notification
     * @return DTO de la notificación
     */
    private NotificationDTO mapToDTO(Notification notification) {
        NotificationDTO dto = new NotificationDTO();
        dto.setId(notification.getId());
        dto.setRecipientId(notification.getRecipientId());
        dto.setSenderId(notification.getSenderId());
        dto.setSenderName(notification.getSenderName());
        dto.setTitle(notification.getTitle());
        dto.setMessage(notification.getMessage());

        // El estado de lectura se maneja de forma especial para notificaciones
        // BROADCAST
        // en el método getLatestNotificationsWithReadStatus
        dto.setRead(notification.isRead());

        dto.setAvatar(notification.getAvatar());
        dto.setType(notification.getType());
        dto.setCategory(notification.getCategory());
        dto.setCreatedAt(notification.getCreatedAt());

        // Formatear el tiempo para mostrar (ej: "Hace 5 minutos")
        dto.setTime(formatTimeAgo(notification.getCreatedAt()));

        return dto;
    }

    /**
     * Formatea una fecha como texto relativo (ej: "Hace 5 minutos")
     *
     * @param dateTime Fecha a formatear
     * @return Texto formateado
     */
    private String formatTimeAgo(LocalDateTime dateTime) {
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(dateTime, now).toMinutes();

        if (minutes < 1) {
            return "Ahora mismo";
        } else if (minutes < 60) {
            return "Hace " + minutes + " minuto" + (minutes > 1 ? "s" : "");
        } else if (minutes < 1440) { // menos de un día
            long hours = minutes / 60;
            return "Hace " + hours + " hora" + (hours > 1 ? "s" : "");
        } else if (minutes < 10080) { // menos de una semana
            long days = minutes / 1440;
            return "Hace " + days + " día" + (days > 1 ? "s" : "");
        } else {
            // Formatear como fecha
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return dateTime.format(formatter);
        }
    }

    /**
     * Obtiene la lista de usuarios que han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Lista de usuarios que han leído la notificación
     */
    public List<NotificationReadUserDTO> getUsersWhoReadNotification(Long notificationId) {
        try {
            List<Object[]> results = notificationReadRepository.findUsersWhoReadNotification(notificationId);

            return results.stream()
                    .map(result -> new NotificationReadUserDTO(
                            (Long) result[0], // userId
                            (String) result[1], // userName
                            (String) result[2], // userEmail
                            (LocalDateTime) result[3] // readAt
                    ))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener usuarios que leyeron la notificación {}: {}", notificationId, e.getMessage(),
                    e);
            return new ArrayList<>();
        }
    }

    /**
     * Obtiene el número de usuarios que han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Número de usuarios que han leído la notificación
     */
    public long getReadCount(Long notificationId) {
        try {
            return notificationReadRepository.countUsersWhoReadNotification(notificationId);
        } catch (Exception e) {
            log.error("Error al obtener el conteo de lecturas para la notificación {}: {}", notificationId,
                    e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Envía notificaciones a usuarios por rol específico
     *
     * @param role       Rol de los usuarios destinatarios
     * @param title      Título de la notificación
     * @param message    Mensaje de la notificación
     * @param senderName Nombre del remitente
     * @return Número de notificaciones enviadas
     */
    @Transactional
    public int sendNotificationByRole(String role, String title, String message, String senderName) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            List<User> users = userRepository.findByRoleAndEstado(roleEnum, "A");

            if (users.isEmpty()) {
                log.warn("No se encontraron usuarios activos con rol: {}", role);
                return 0;
            }

            int notificationsSent = 0;
            for (User user : users) {
                try {
                    // Crear notificación individual para cada usuario
                    Notification notification = new Notification();
                    notification.setRecipient(user);
                    notification.setSenderName(senderName);
                    notification.setTitle(title);
                    notification.setMessage(message);
                    notification.setType(Notification.NotificationType.ROLE_BASED);
                    notification.setCategory(Notification.NotificationCategory.GENERAL);
                    notification.setCreatedAt(LocalDateTime.now());
                    notification.setRead(false);
                    notification.setData(role); // Guardar el rol en el campo data

                    Notification savedNotification = notificationRepository.save(notification);

                    // Enviar notificación en tiempo real
                    NotificationDTO dto = mapToDTO(savedNotification);
                    messagingTemplate.convertAndSendToUser(
                            user.getId().toString(),
                            "/queue/notifications",
                            dto);

                    notificationsSent++;
                } catch (Exception e) {
                    log.error("Error al enviar notificación al usuario {}: {}", user.getId(), e.getMessage());
                }
            }

            log.info("Enviadas {} notificaciones a usuarios con rol {}", notificationsSent, role);
            return notificationsSent;

        } catch (IllegalArgumentException e) {
            log.error("Rol inválido: {}", role);
            throw new RuntimeException("Rol inválido: " + role);
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por rol {}: {}", role, e.getMessage(), e);
            throw new RuntimeException("Error al enviar notificaciones por rol: " + e.getMessage());
        }
    }

    /**
     * Envía notificaciones a usuarios por sede específica
     *
     * @param sedeId     ID de la sede
     * @param title      Título de la notificación
     * @param message    Mensaje de la notificación
     * @param senderName Nombre del remitente
     * @return Número de notificaciones enviadas
     */
    @Transactional
    public int sendNotificationBySede(Long sedeId, String title, String message, String senderName) {
        try {
            List<User> users = userRepository.findBySedeIdAndEstado(sedeId, "A");

            if (users.isEmpty()) {
                log.warn("No se encontraron usuarios activos en la sede: {}", sedeId);
                return 0;
            }

            int notificationsSent = 0;
            for (User user : users) {
                try {
                    // Crear notificación individual para cada usuario
                    Notification notification = new Notification();
                    notification.setRecipient(user);
                    notification.setSenderName(senderName);
                    notification.setTitle(title);
                    notification.setMessage(message);
                    notification.setType(Notification.NotificationType.ROLE_BASED);
                    notification.setCategory(Notification.NotificationCategory.GENERAL);
                    notification.setCreatedAt(LocalDateTime.now());
                    notification.setRead(false);
                    notification.setData(sedeId.toString()); // Guardar el ID de sede en el campo data

                    Notification savedNotification = notificationRepository.save(notification);

                    // Enviar notificación en tiempo real
                    NotificationDTO dto = mapToDTO(savedNotification);
                    messagingTemplate.convertAndSendToUser(
                            user.getId().toString(),
                            "/queue/notifications",
                            dto);

                    notificationsSent++;
                } catch (Exception e) {
                    log.error("Error al enviar notificación al usuario {}: {}", user.getId(), e.getMessage());
                }
            }

            log.info("Enviadas {} notificaciones a usuarios de la sede {}", notificationsSent, sedeId);
            return notificationsSent;

        } catch (Exception e) {
            log.error("Error al enviar notificaciones por sede {}: {}", sedeId, e.getMessage(), e);
            throw new RuntimeException("Error al enviar notificaciones por sede: " + e.getMessage());
        }
    }

    /**
     * Envía notificaciones a usuarios por sede y rol específicos
     *
     * @param sedeId     ID de la sede
     * @param role       Rol de los usuarios destinatarios
     * @param title      Título de la notificación
     * @param message    Mensaje de la notificación
     * @param senderName Nombre del remitente
     * @return Número de notificaciones enviadas
     */
    @Transactional
    public int sendNotificationBySedeAndRole(Long sedeId, String role, String title, String message,
            String senderName) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            List<User> users = userRepository.findBySedeIdAndRoleAndEstado(sedeId, roleEnum, "A");

            if (users.isEmpty()) {
                log.warn("No se encontraron usuarios activos con rol {} en la sede: {}", role, sedeId);
                return 0;
            }

            int notificationsSent = 0;
            for (User user : users) {
                try {
                    // Crear notificación individual para cada usuario
                    Notification notification = new Notification();
                    notification.setRecipient(user);
                    notification.setSenderName(senderName);
                    notification.setTitle(title);
                    notification.setMessage(message);
                    notification.setType(Notification.NotificationType.ROLE_BASED);
                    notification.setCategory(Notification.NotificationCategory.GENERAL);
                    notification.setCreatedAt(LocalDateTime.now());
                    notification.setRead(false);
                    notification.setData(sedeId + ":" + role); // Guardar sede:rol en el campo data

                    Notification savedNotification = notificationRepository.save(notification);

                    // Enviar notificación en tiempo real
                    NotificationDTO dto = mapToDTO(savedNotification);
                    messagingTemplate.convertAndSendToUser(
                            user.getId().toString(),
                            "/queue/notifications",
                            dto);

                    notificationsSent++;
                } catch (Exception e) {
                    log.error("Error al enviar notificación al usuario {}: {}", user.getId(), e.getMessage());
                }
            }

            log.info("Enviadas {} notificaciones a usuarios con rol {} de la sede {}", notificationsSent, role, sedeId);
            return notificationsSent;

        } catch (IllegalArgumentException e) {
            log.error("Rol inválido: {}", role);
            throw new RuntimeException("Rol inválido: " + role);
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por sede {} y rol {}: {}", sedeId, role, e.getMessage(), e);
            throw new RuntimeException("Error al enviar notificaciones por sede y rol: " + e.getMessage());
        }
    }
}
